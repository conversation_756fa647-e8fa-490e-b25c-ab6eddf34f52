#!/usr/bin/env python3
"""
测试Replicate集成的完整流程
"""
import asyncio
import logging
import sys
import os

# 添加路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

from url_parser import RedditUrlParser
from data_crawler import DataCrawler
from semantic_analyzer import ProfileSemanticAnalyzer
from graph_builder import PersonalityGraphBuilder

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

async def test_full_pipeline():
    """测试完整的分析流程"""
    
    # 测试URL
    test_url = "https://www.reddit.com/r/cursor/comments/1lstb9t/comment/n1lgpq6/?utm_source=share&utm_medium=web3x&utm_name=web3xcss&utm_term=1&utm_content=share_button"
    
    logger.info(f"开始测试完整流程，URL: {test_url}")
    
    try:
        # 1. URL解析
        logger.info("步骤1: URL解析")
        url_parser = RedditUrlParser()
        parse_result = url_parser.parse_reddit_url(test_url)
        
        if not parse_result["success"]:
            logger.error(f"URL解析失败: {parse_result['error']}")
            return
        
        logger.info(f"解析结果: {parse_result}")
        
        # 2. 数据抓取
        logger.info("步骤2: 数据抓取")
        crawler = DataCrawler()
        crawl_result = await crawler.crawl_user_data(parse_result["username"])
        
        if not crawl_result["success"]:
            logger.error(f"数据抓取失败: {crawl_result['error']}")
            return
        
        reddit_user = crawl_result["reddit_user"]
        logger.info(f"抓取到用户数据: {reddit_user.username}, 帖子数: {len(reddit_user.posts)}, 评论数: {len(reddit_user.comments)}")
        
        # 3. 语义分析
        logger.info("步骤3: 语义分析")
        semantic_analyzer = ProfileSemanticAnalyzer()
        semantic_result = await semantic_analyzer.analyze_user_content(reddit_user)
        
        if not semantic_result["success"]:
            logger.error(f"语义分析失败: {semantic_result['error']}")
            return
        
        logger.info(f"语义分析完成，置信度: {semantic_result.get('stats', {}).get('confidence_score', 'N/A')}")
        
        # 4. 图谱构建
        logger.info("步骤4: 图谱构建")
        graph_builder = PersonalityGraphBuilder()
        graph_result = await graph_builder.build_personality_graph(reddit_user, semantic_result)
        
        if not graph_result["success"]:
            logger.error(f"图谱构建失败: {graph_result['error']}")
            return
        
        logger.info(f"图谱构建完成，节点数: {len(graph_result['user_profile'].graph.nodes)}")
        
        # 5. 输出结果摘要
        logger.info("=" * 50)
        logger.info("测试完成！结果摘要:")
        logger.info(f"用户: {reddit_user.username}")
        logger.info(f"内容统计: {len(reddit_user.posts)}帖子, {len(reddit_user.comments)}评论")
        logger.info(f"图谱节点: {len(graph_result['user_profile'].graph.nodes)}")
        logger.info(f"图谱边: {len(graph_result['user_profile'].graph.edges)}")
        logger.info(f"质量指标: {graph_result.get('quality_metrics', {})}")
        logger.info("=" * 50)
        
        # 关闭服务
        await semantic_analyzer.close()
        await graph_builder.close()
        await crawler.close()
        
        return True
        
    except Exception as e:
        logger.error(f"测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_semantic_analyzer_only():
    """仅测试语义分析器"""
    logger.info("测试语义分析器...")
    
    try:
        # 创建模拟用户数据
        from resona.models import RedditUser, RedditPost, RedditComment
        from datetime import datetime
        
        test_user = RedditUser(
            username="test_user",
            posts=[
                RedditPost(
                    id="test1",
                    text="I'm really struggling with my career choice. Should I stay in my current job or take a risk with a startup?",
                    timestamp=datetime.now(),
                    subreddit="careerguidance",
                    score=10
                )
            ],
            comments=[
                RedditComment(
                    id="test2", 
                    text="Thanks for the advice. I'm feeling anxious about making the wrong decision.",
                    timestamp=datetime.now(),
                    subreddit="careerguidance",
                    score=5
                )
            ]
        )
        
        # 测试语义分析
        semantic_analyzer = ProfileSemanticAnalyzer()
        result = await semantic_analyzer.analyze_user_content(test_user)
        
        logger.info(f"语义分析结果: {result['success']}")
        if result["success"]:
            parsed_query = result["parsed_query"]
            logger.info(f"话题: {parsed_query.topics}")
            logger.info(f"情绪状态: {parsed_query.emotional_state}")
            logger.info(f"置信度: {parsed_query.confidence}")
        
        await semantic_analyzer.close()
        return result["success"]
        
    except Exception as e:
        logger.error(f"语义分析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("选择测试模式:")
    print("1. 完整流程测试")
    print("2. 仅语义分析测试")
    
    choice = input("请输入选择 (1 或 2): ").strip()
    
    if choice == "1":
        success = asyncio.run(test_full_pipeline())
    elif choice == "2":
        success = asyncio.run(test_semantic_analyzer_only())
    else:
        print("无效选择")
        success = False
    
    if success:
        print("✅ 测试成功!")
    else:
        print("❌ 测试失败!")
