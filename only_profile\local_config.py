"""
本地配置文件 - 用于only_profile服务
"""
import os
from typing import Optional

class LocalConfig:
    """本地配置类"""
    
    def __init__(self):
        # Replicate API配置
        self.REPLICATE_API_TOKEN = os.getenv("REPLICATE_API_TOKEN", "")
        
        # 模型配置
        self.LLM_MODEL = os.getenv("LLM_MODEL", "openai/gpt-4.1-mini")
        self.EMBEDDING_MODEL = os.getenv("EMBEDDING_MODEL", "sentence-transformers/all-mpnet-base-v2")
        
        # Reddit API配置
        self.REDDIT_CLIENT_ID = os.getenv("REDDIT_CLIENT_ID", "")
        self.REDDIT_CLIENT_SECRET = os.getenv("REDDIT_CLIENT_SECRET", "")
        self.REDDIT_USER_AGENT = os.getenv("REDDIT_USER_AGENT", "OnlyProfile/1.0")
        
        # 分析配置
        self.REDDIT_HISTORY_LIMIT = int(os.getenv("REDDIT_HISTORY_LIMIT", "200"))
        self.MAX_POSTS_PER_USER = int(os.getenv("MAX_POSTS_PER_USER", "200"))
        self.MAX_COMMENTS_PER_USER = int(os.getenv("MAX_COMMENTS_PER_USER", "300"))
        self.ANALYSIS_TIMEOUT = int(os.getenv("ANALYSIS_TIMEOUT", "300"))
        
        # 服务器配置
        self.HOST = os.getenv("HOST", "127.0.0.1")
        self.PORT = int(os.getenv("PORT", "5000"))
        self.DEBUG = os.getenv("DEBUG", "True").lower() == "true"
        
        # 代理配置
        self.HTTP_PROXY = os.getenv("HTTP_PROXY", "")
        self.HTTPS_PROXY = os.getenv("HTTPS_PROXY", "")

# 创建全局配置实例
config = LocalConfig()
