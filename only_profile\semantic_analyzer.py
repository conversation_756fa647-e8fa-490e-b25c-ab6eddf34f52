"""
语义分析模块
复用resona的语义分析器和AI服务，针对Reddit用户内容进行分析
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
import sys
import os

# 导入本地模块
from replicate_server import ReplicateAIService
from local_config import config

# 添加resona路径（用于数据模型）
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

# 尝试导入resona模型，如果失败则使用本地模型
try:
    from resona.models import RedditUser, RedditPost, RedditComment, ParsedQuery
except ImportError:
    # 创建本地模型定义
    from typing import List, Dict, Any, Optional
    from pydantic import BaseModel, Field
    from datetime import datetime

    class RedditPost(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditComment(BaseModel):
        id: str = ""
        text: str
        timestamp: datetime = Field(default_factory=datetime.now)
        subreddit: str = ""
        score: int = 0

    class RedditUser(BaseModel):
        username: str
        posts: List[RedditPost] = Field(default_factory=list)
        comments: List[RedditComment] = Field(default_factory=list)

    class ParsedQuery(BaseModel):
        original_text: str = Field(..., description="原始输入文本")
        search_intent: List[str] = Field(default_factory=list, description="Reddit搜索关键词")
        values_info: Dict[str, Any] = Field(default_factory=dict, description="初始价值观信息")
        emotional_state: Dict[str, float] = Field(default_factory=dict, description="情绪状态评分")
        topics: List[str] = Field(default_factory=list, description="主要话题")
        confidence: float = Field(default=1.0, description="解析置信度")

        # 新增深度分析字段
        core_concerns: List[str] = Field(default_factory=list, description="核心关切点")
        decision_points: List[str] = Field(default_factory=list, description="面临的决策点")
        life_domains: List[str] = Field(default_factory=list, description="涉及的生活领域")
        support_needs: List[str] = Field(default_factory=list, description="需要的支持类型")

logger = logging.getLogger(__name__)

class ProfileSemanticAnalyzer:
    """用户档案语义分析器 - 专门分析Reddit用户内容"""
    
    def __init__(self):
        """初始化语义分析器"""
        self.ai_service = ReplicateAIService()
        # 不再使用resona的SemanticAnalyzer，直接使用Replicate服务
        
        # 分析配置
        self.max_content_length = 8000  # 单次分析最大内容长度
        self.min_content_threshold = 100  # 最少内容阈值
        self.content_sample_size = 10  # 内容采样大小
        
        logger.info("用户档案语义分析器初始化完成")
    
    async def analyze_user_content(self, reddit_user: RedditUser) -> Dict[str, Any]:
        """
        分析Reddit用户内容
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 分析结果
        """
        logger.info(f"开始分析用户内容: {reddit_user.username}")
        
        try:
            # 1. 准备用户内容
            user_content = self._prepare_user_content(reddit_user)
            
            if not user_content or len(user_content) < self.min_content_threshold:
                return {
                    "success": False,
                    "error": "用户内容不足，无法进行有效分析",
                    "username": reddit_user.username,
                    "content_length": len(user_content) if user_content else 0
                }
            
            # 2. 生成内容摘要（暂时不使用）
            content_summary = self._generate_user_content_summary(user_content)
            
            # 3. 使用AI服务进行语义分析
            logger.info("开始语义分析...")
            parsed_query = await self._parse_user_content_with_ai(content_summary)
            
            # 4. 深度分析用户特征
            logger.info("开始深度用户特征分析...")
            user_characteristics = await self._analyze_user_characteristics(user_content, parsed_query)
            
            # 5. 分析内容模式
            logger.info("开始内容模式分析...")
            content_patterns = await self._analyze_content_patterns(reddit_user)
            
            # 6. 生成综合分析报告
            analysis_report = self._generate_analysis_report(
                reddit_user.username,
                parsed_query,
                user_characteristics,
                content_patterns
            )
            
            logger.info(f"用户内容分析完成: {reddit_user.username}")
            
            return {
                "success": True,
                "username": reddit_user.username,
                "parsed_query": parsed_query,
                "user_characteristics": user_characteristics,
                "content_patterns": content_patterns,
                "analysis_report": analysis_report,
                "content_summary": content_summary,
                "stats": {
                    "content_length": len(user_content),
                    "posts_analyzed": len(reddit_user.posts),
                    "comments_analyzed": len(reddit_user.comments),
                    "confidence_score": parsed_query.confidence
                }
            }
            
        except Exception as e:
            logger.error(f"分析用户内容时发生错误: {e}")
            return {
                "success": False,
                "error": f"分析失败: {str(e)}",
                "username": reddit_user.username,
                "stats": {
                    "content_length": 0,
                    "posts_analyzed": 0,
                    "comments_analyzed": 0,
                    "confidence_score": 0.0
                }
            }
    
    def _prepare_user_content(self, reddit_user: RedditUser) -> str:
        """
        准备用户内容进行分析
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            str: 处理后的用户内容
        """
        all_content = []
        
        # 收集帖子内容
        for post in reddit_user.posts:
            if len(post.text) > 50:  # 过滤太短的内容
                content_piece = f"[帖子 r/{post.subreddit}] {post.text}"
                all_content.append(content_piece)
        
        # 收集评论内容
        for comment in reddit_user.comments:
            if len(comment.text) > 30:  # 过滤太短的评论
                content_piece = f"[评论 r/{comment.subreddit}] {comment.text}"
                all_content.append(content_piece)
        
        # 按时间排序（最新的在前）
        all_posts_comments = reddit_user.posts + reddit_user.comments
        all_posts_comments.sort(key=lambda x: x.timestamp, reverse=True)
        
        # 重新组织内容（按时间顺序）
        organized_content = []
        for item in all_posts_comments:
            if isinstance(item, RedditPost) and len(item.text) > 50:
                organized_content.append(f"[帖子 r/{item.subreddit}] {item.text}")
            elif isinstance(item, RedditComment) and len(item.text) > 30:
                organized_content.append(f"[评论 r/{item.subreddit}] {item.text}")
        
        # 合并内容，限制长度
        combined_content = "\n\n".join(organized_content)
        
        # 如果内容过长，进行智能截取
        if len(combined_content) > self.max_content_length:
            # 优先保留最新的内容
            truncated_content = combined_content[:self.max_content_length]
            # 确保不在单词中间截断
            last_space = truncated_content.rfind(' ')
            if last_space > self.max_content_length * 0.8:
                truncated_content = truncated_content[:last_space]
            combined_content = truncated_content + "..."
        
        return combined_content
    
    def _generate_user_content_summary(self, content: str) -> str:
        """
        生成用户内容摘要，用于语义分析
        
        Args:
            content: 用户内容
            
        Returns:
            str: 内容摘要
        """
        # 简单的摘要生成 - 取前几段和关键信息
        lines = content.split('\n')
        important_lines = []
        
        for line in lines:
            if line.strip():
                # 过滤掉过短的行
                if len(line.strip()) > 20:
                    important_lines.append(line.strip())
                    
                # 限制行数
                if len(important_lines) >= 15:
                    break
        
        summary = '\n'.join(important_lines)
        
        # 如果摘要还是太长，进一步压缩
        if len(summary) > 2000:
            summary = summary[:2000] + "..."
        
        return summary

    async def _parse_user_content_with_ai(self, content: str) -> ParsedQuery:
        """
        使用AI服务解析用户内容，替换原有的语义分析器

        Args:
            content: 用户内容摘要

        Returns:
            ParsedQuery: 解析结果
        """
        try:
            prompt = f"""
请分析以下Reddit用户的发言内容，提取关键信息：

用户内容：
{content}

请以JSON格式返回分析结果：
{{
    "topics": ["主题1", "主题2", "主题3"],
    "emotional_state": {{
        "joy": 0.2,
        "sadness": 0.1,
        "anger": 0.0,
        "fear": 0.3,
        "surprise": 0.1,
        "disgust": 0.0,
        "trust": 0.3,
        "anxiety": 0.2,
        "confusion": 0.1
    }},
    "intent": "用户意图描述",
    "confidence": 0.85,
    "core_concerns": ["关切点1", "关切点2"],
    "decision_points": ["决策点1", "决策点2"],
    "life_domains": ["生活领域1", "生活领域2"],
    "support_needs": ["支持需求1", "支持需求2"]
}}

请基于内容客观分析，避免过度推测。
"""

            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=1000,
                temperature=0.3,
                system_prompt="你是一个专业的心理分析师和语义解析专家。请严格按照JSON格式返回分析结果。"
            )

            # 解析AI响应
            import json
            result = json.loads(self.ai_service._extract_json_from_response(response))

            # 构建ParsedQuery对象
            parsed_query = ParsedQuery(
                original_text=content,
                topics=result.get("topics", ["general"]),
                emotional_state=result.get("emotional_state", {"neutral": 0.5}),
                confidence=result.get("confidence", 0.5),
                core_concerns=result.get("core_concerns", ["寻求理解"]),
                decision_points=result.get("decision_points", ["人生选择"]),
                life_domains=result.get("life_domains", ["general"]),
                support_needs=result.get("support_needs", ["建议"])
            )

            return parsed_query

        except Exception as e:
            logger.error(f"AI语义分析失败: {e}")
            # 返回默认的ParsedQuery
            return ParsedQuery(
                original_text=content,
                topics=["general"],
                emotional_state={"neutral": 0.5},
                confidence=0.3,
                core_concerns=["寻求理解"],
                decision_points=["人生选择"],
                life_domains=["general"],
                support_needs=["建议"]
            )
    
    async def _analyze_user_characteristics(self, content: str, parsed_query: ParsedQuery) -> Dict[str, Any]:
        """
        深度分析用户特征
        
        Args:
            content: 用户内容
            parsed_query: 语义分析结果
            
        Returns:
            Dict: 用户特征分析
        """
        try:
            # 构建用户特征分析提示
            prompt = f"""
请分析以下Reddit用户的内容，提取用户的深层特征和性格画像。

用户内容：
{content[:3000]}...

请以JSON格式返回分析结果：
{{
    "personality_traits": {{
        "openness": 0.7,
        "conscientiousness": 0.6,
        "extraversion": 0.4,
        "agreeableness": 0.8,
        "neuroticism": 0.3
    }},
    "communication_style": {{
        "formality": "casual|formal|mixed",
        "tone": "positive|negative|neutral|mixed",
        "verbosity": "concise|detailed|varies",
        "emotional_expression": "high|medium|low"
    }},
    "interests_and_values": {{
        "main_interests": ["兴趣1", "兴趣2", "兴趣3"],
        "core_values": ["价值观1", "价值观2", "价值观3"],
        "life_priorities": ["优先级1", "优先级2", "优先级3"]
    }},
    "behavioral_patterns": {{
        "posting_motivation": "help_seeking|knowledge_sharing|social_interaction|entertainment",
        "engagement_style": "active|passive|selective",
        "conflict_handling": "avoidant|confrontational|diplomatic|varies"
    }},
    "psychological_indicators": {{
        "stress_level": "low|medium|high",
        "life_satisfaction": "low|medium|high",
        "social_connectivity": "isolated|somewhat_connected|well_connected",
        "future_orientation": "pessimistic|realistic|optimistic"
    }},
    "confidence_score": 0.85
}}

请基于文本内容客观分析，避免过度推测。
"""
            
            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=2000,
                temperature=0.3
            )
            
            # 解析AI响应
            import json
            user_characteristics = json.loads(self.ai_service._extract_json_from_response(response))
            
            return user_characteristics
            
        except Exception as e:
            logger.error(f"用户特征分析失败: {e}")
            # 返回默认结构
            return {
                "personality_traits": {
                    "openness": 0.5,
                    "conscientiousness": 0.5,
                    "extraversion": 0.5,
                    "agreeableness": 0.5,
                    "neuroticism": 0.5
                },
                "communication_style": {
                    "formality": "mixed",
                    "tone": "neutral",
                    "verbosity": "varies",
                    "emotional_expression": "medium"
                },
                "interests_and_values": {
                    "main_interests": ["未知"],
                    "core_values": ["未知"],
                    "life_priorities": ["未知"]
                },
                "behavioral_patterns": {
                    "posting_motivation": "unknown",
                    "engagement_style": "unknown",
                    "conflict_handling": "unknown"
                },
                "psychological_indicators": {
                    "stress_level": "unknown",
                    "life_satisfaction": "unknown",
                    "social_connectivity": "unknown",
                    "future_orientation": "unknown"
                },
                "confidence_score": 0.3
            }
    
    async def _analyze_content_patterns(self, reddit_user: RedditUser) -> Dict[str, Any]:
        """
        分析内容模式
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 内容模式分析
        """
        try:
            # 统计基本信息
            subreddit_distribution = {}
            post_lengths = []
            comment_lengths = []
            posting_times = []
            
            # 分析帖子
            for post in reddit_user.posts:
                subreddit = post.subreddit
                subreddit_distribution[subreddit] = subreddit_distribution.get(subreddit, 0) + 1
                post_lengths.append(len(post.text))
                posting_times.append(post.timestamp)
            
            # 分析评论
            for comment in reddit_user.comments:
                subreddit = comment.subreddit
                subreddit_distribution[subreddit] = subreddit_distribution.get(subreddit, 0) + 1
                comment_lengths.append(len(comment.text))
                posting_times.append(comment.timestamp)
            
            # 计算统计信息
            patterns = {
                "posting_frequency": {
                    "total_posts": len(reddit_user.posts),
                    "total_comments": len(reddit_user.comments),
                    "posts_to_comments_ratio": len(reddit_user.posts) / max(len(reddit_user.comments), 1)
                },
                "content_length": {
                    "avg_post_length": sum(post_lengths) / len(post_lengths) if post_lengths else 0,
                    "avg_comment_length": sum(comment_lengths) / len(comment_lengths) if comment_lengths else 0,
                    "max_post_length": max(post_lengths) if post_lengths else 0,
                    "max_comment_length": max(comment_lengths) if comment_lengths else 0
                },
                "subreddit_diversity": {
                    "unique_subreddits": len(subreddit_distribution),
                    "top_subreddits": sorted(subreddit_distribution.items(), key=lambda x: x[1], reverse=True)[:5],
                    "distribution": subreddit_distribution
                },
                "temporal_patterns": {
                    "date_range": {
                        "earliest": min(posting_times) if posting_times else None,
                        "latest": max(posting_times) if posting_times else None
                    },
                    "activity_span_days": (max(posting_times) - min(posting_times)).days if len(posting_times) > 1 else 0
                }
            }
            
            return patterns
            
        except Exception as e:
            logger.error(f"内容模式分析失败: {e}")
            return {
                "posting_frequency": {"total_posts": 0, "total_comments": 0},
                "content_length": {"avg_post_length": 0, "avg_comment_length": 0},
                "subreddit_diversity": {"unique_subreddits": 0, "top_subreddits": []},
                "temporal_patterns": {"date_range": {}, "activity_span_days": 0}
            }
    
    def _generate_analysis_report(self, username: str, parsed_query: ParsedQuery, 
                                user_characteristics: Dict[str, Any], 
                                content_patterns: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成综合分析报告
        
        Args:
            username: 用户名
            parsed_query: 语义分析结果
            user_characteristics: 用户特征
            content_patterns: 内容模式
            
        Returns:
            Dict: 综合分析报告
        """
        report = {
            "username": username,
            "analysis_timestamp": datetime.now(),
            "overall_assessment": {
                "confidence_level": (parsed_query.confidence + user_characteristics.get("confidence_score", 0.5)) / 2,
                "data_quality": self._assess_data_quality(content_patterns),
                "analysis_reliability": "high" if parsed_query.confidence > 0.7 else "medium" if parsed_query.confidence > 0.5 else "low"
            },
            "key_insights": {
                "dominant_emotions": self._extract_dominant_emotions(parsed_query.emotional_state),
                "primary_topics": parsed_query.topics[:3],
                "personality_summary": self._summarize_personality(user_characteristics.get("personality_traits", {})),
                "engagement_style": user_characteristics.get("behavioral_patterns", {}).get("engagement_style", "unknown")
            },
            "recommendations": {
                "communication_approach": self._suggest_communication_approach(user_characteristics),
                "potential_common_ground": self._identify_common_ground(parsed_query, user_characteristics),
                "interaction_tips": self._generate_interaction_tips(user_characteristics)
            }
        }
        
        return report
    
    def _assess_data_quality(self, content_patterns: Dict[str, Any]) -> str:
        """评估数据质量"""
        total_content = content_patterns.get("posting_frequency", {}).get("total_posts", 0) + \
                       content_patterns.get("posting_frequency", {}).get("total_comments", 0)
        
        if total_content >= 20:
            return "high"
        elif total_content >= 10:
            return "medium"
        else:
            return "low"
    
    def _extract_dominant_emotions(self, emotional_state: Dict[str, float]) -> List[str]:
        """提取主要情绪"""
        if not emotional_state:
            return ["neutral"]
        
        # 按情绪强度排序
        sorted_emotions = sorted(emotional_state.items(), key=lambda x: x[1], reverse=True)
        
        # 取前2-3个显著情绪
        dominant = []
        for emotion, intensity in sorted_emotions[:3]:
            if intensity > 0.3:  # 显著性阈值
                dominant.append(emotion)
        
        return dominant if dominant else ["neutral"]
    
    def _summarize_personality(self, personality_traits: Dict[str, float]) -> str:
        """总结性格特征"""
        if not personality_traits:
            return "性格特征不明确"
        
        # 找出最突出的特征
        max_trait = max(personality_traits.items(), key=lambda x: x[1])
        min_trait = min(personality_traits.items(), key=lambda x: x[1])
        
        trait_descriptions = {
            "openness": "开放性",
            "conscientiousness": "责任感",
            "extraversion": "外向性",
            "agreeableness": "亲和性",
            "neuroticism": "神经质"
        }
        
        return f"偏向{trait_descriptions.get(max_trait[0], max_trait[0])}，较少{trait_descriptions.get(min_trait[0], min_trait[0])}"
    
    def _suggest_communication_approach(self, user_characteristics: Dict[str, Any]) -> str:
        """建议沟通方式"""
        comm_style = user_characteristics.get("communication_style", {})
        behavioral = user_characteristics.get("behavioral_patterns", {})
        
        if comm_style.get("formality") == "formal":
            return "使用正式、专业的语言"
        elif comm_style.get("emotional_expression") == "high":
            return "可以使用情感丰富的表达方式"
        elif behavioral.get("engagement_style") == "passive":
            return "采用温和、鼓励性的语调"
        else:
            return "保持友好、自然的沟通风格"
    
    def _identify_common_ground(self, parsed_query: ParsedQuery, user_characteristics: Dict[str, Any]) -> List[str]:
        """识别共同点"""
        common_ground = []
        
        # 从话题中提取
        if parsed_query.topics:
            common_ground.extend(parsed_query.topics[:2])
        
        # 从兴趣中提取
        interests = user_characteristics.get("interests_and_values", {}).get("main_interests", [])
        if interests:
            common_ground.extend(interests[:2])
        
        return common_ground[:3] if common_ground else ["生活经验分享"]
    
    def _generate_interaction_tips(self, user_characteristics: Dict[str, Any]) -> List[str]:
        """生成交互建议"""
        tips = []
        
        psych_indicators = user_characteristics.get("psychological_indicators", {})
        behavioral = user_characteristics.get("behavioral_patterns", {})
        
        if psych_indicators.get("stress_level") == "high":
            tips.append("注意提供情感支持")
        
        if behavioral.get("posting_motivation") == "help_seeking":
            tips.append("可以分享相关经验和建议")
        
        if psych_indicators.get("social_connectivity") == "isolated":
            tips.append("采用包容、理解的态度")
        
        return tips if tips else ["保持开放和友好的交流态度"]
    
    async def close(self):
        """关闭服务"""
        try:
            await self.ai_service.close()
            logger.info("语义分析器已关闭")
        except Exception as e:
            logger.error(f"关闭语义分析器时发生错误: {e}")

# 便捷函数
async def analyze_reddit_user(reddit_user: RedditUser) -> Dict[str, Any]:
    """
    便捷函数：分析Reddit用户
    
    Args:
        reddit_user: Reddit用户数据
        
    Returns:
        Dict: 分析结果
    """
    analyzer = ProfileSemanticAnalyzer()
    try:
        result = await analyzer.analyze_user_content(reddit_user)
        return result
    finally:
        await analyzer.close()

if __name__ == "__main__":
    # 测试代码
    from resona.models import RedditPost, RedditComment
    
    async def test_analyzer():
        # 创建测试用户数据
        test_posts = [
            RedditPost(
                id="test1",
                text="I've been struggling with career decisions lately. Not sure if I should continue my current job or pursue further education.",
                timestamp=datetime.now(),
                subreddit="careerguidance",
                score=5
            )
        ]
        
        test_comments = [
            RedditComment(
                id="test2",
                text="This is really helpful advice. I've been in a similar situation and found that taking time to reflect on your values really helps.",
                timestamp=datetime.now(),
                subreddit="advice",
                score=3
            )
        ]
        
        test_user = RedditUser(
            username="testuser",
            posts=test_posts,
            comments=test_comments
        )
        
        analyzer = ProfileSemanticAnalyzer()
        
        try:
            result = await analyzer.analyze_user_content(test_user)
            print(f"分析结果: {result['success']}")
            if result['success']:
                print(f"置信度: {result['stats']['confidence_score']:.3f}")
                print(f"主要话题: {result['parsed_query'].topics}")
                print(f"情绪状态: {result['parsed_query'].emotional_state}")
            else:
                print(f"分析失败: {result['error']}")
                
        finally:
            await analyzer.close()
    
    # 运行测试
    # asyncio.run(test_analyzer()) 