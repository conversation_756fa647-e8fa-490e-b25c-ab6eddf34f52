"""
人格图谱构建模块
复用resona的graph_builder和user_profiler，将Reddit用户数据和语义分析结果转换为人格图谱
"""
import asyncio
import logging
from typing import List, Dict, Optional, Any, Tuple
from datetime import datetime
import sys
import os

# 导入本地模块
from replicate_server import ReplicateAIService

# 添加resona路径（用于数据模型）
sys.path.insert(0, os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 'resona'))

from resona.models import (
    RedditUser, ParsedQuery, UserProfile, UserGraph,
    NodeType, RelationType, GraphNode, GraphEdge
)

logger = logging.getLogger(__name__)

class PersonalityGraphBuilder:
    """人格图谱构建器 - 将Reddit用户数据转换为人格图谱"""
    
    def __init__(self):
        """初始化人格图谱构建器"""
        self.ai_service = ReplicateAIService()
        # 不再使用resona的复杂组件，直接使用Replicate服务进行图谱构建
        
        # 图谱构建配置
        self.max_content_per_analysis = 5000  # 单次分析最大内容长度
        self.min_graph_nodes = 5  # 最小图节点数
        self.max_graph_complexity = 50  # 最大图复杂度
        
        logger.info("人格图谱构建器初始化完成")
    
    async def build_personality_graph(self, reddit_user: RedditUser, 
                                    semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        构建人格图谱
        
        Args:
            reddit_user: Reddit用户数据
            semantic_analysis: 语义分析结果
            
        Returns:
            Dict: 图谱构建结果
        """
        logger.info(f"开始构建人格图谱: {reddit_user.username}")
        
        try:
            # 1. 准备内容数据
            content_data = self._prepare_content_for_graph(reddit_user)
            
            if not content_data:
                return {
                    "success": False,
                    "error": "无足够内容构建图谱",
                    "username": reddit_user.username
                }
            
            # 2. 使用AI构建用户图谱
            logger.info("使用AI构建用户图谱...")
            user_graph = await self._build_user_graph_with_ai(
                content_data["texts"],
                content_data["context"],
                semantic_analysis
            )

            # 3. 构建完整用户画像
            logger.info("构建完整用户画像...")
            user_profile = await self._build_user_profile(
                reddit_user, user_graph, semantic_analysis
            )
            
            # 5. 生成图谱解释
            logger.info("生成图谱解释...")
            graph_explanation = await self._generate_graph_explanation(
                user_profile, semantic_analysis
            )
            
            # 6. 计算图谱质量指标
            quality_metrics = self._calculate_graph_quality(user_profile.graph)
            
            logger.info(f"人格图谱构建完成: {reddit_user.username}")
            
            return {
                "success": True,
                "username": reddit_user.username,
                "user_profile": user_profile,
                "graph_explanation": graph_explanation,
                "quality_metrics": quality_metrics,
                "content_stats": content_data["stats"],
                "build_timestamp": datetime.now()
            }
            
        except Exception as e:
            logger.error(f"构建人格图谱时发生错误: {e}")
            return {
                "success": False,
                "error": f"图谱构建失败: {str(e)}",
                "username": reddit_user.username
            }
    
    def _prepare_content_for_graph(self, reddit_user: RedditUser) -> Optional[Dict[str, Any]]:
        """
        准备内容数据用于图谱构建
        
        Args:
            reddit_user: Reddit用户数据
            
        Returns:
            Dict: 处理后的内容数据
        """
        try:
            # 收集和处理内容
            all_texts = []
            content_metadata = []
            
            # 处理帖子
            for post in reddit_user.posts:
                if len(post.text) > 100:  # 过滤过短的内容
                    text = f"【{post.timestamp.strftime('%Y年%m月')}】在r/{post.subreddit}发帖：{post.text}"
                    all_texts.append(text)
                    content_metadata.append({
                        "type": "post",
                        "subreddit": post.subreddit,
                        "timestamp": post.timestamp,
                        "score": post.score,
                        "length": len(post.text)
                    })
            
            # 处理评论
            for comment in reddit_user.comments:
                if len(comment.text) > 50:  # 过滤过短的评论
                    text = f"【{comment.timestamp.strftime('%Y年%m月')}】在r/{comment.subreddit}评论：{comment.text}"
                    all_texts.append(text)
                    content_metadata.append({
                        "type": "comment",
                        "subreddit": comment.subreddit,
                        "timestamp": comment.timestamp,
                        "score": comment.score,
                        "length": len(comment.text)
                    })
            
            if not all_texts:
                return None
            
            # 按时间排序（保持时间线信息）
            combined_data = list(zip(all_texts, content_metadata))
            combined_data.sort(key=lambda x: x[1]["timestamp"])
            
            all_texts = [item[0] for item in combined_data]
            content_metadata = [item[1] for item in combined_data]
            
            # 生成上下文信息
            context = f"用户{reddit_user.username}的Reddit发言历史，包含{len(reddit_user.posts)}个帖子和{len(reddit_user.comments)}个评论"
            
            # 如果内容过多，进行智能采样
            if len(all_texts) > 20:
                # 优先保留评分高的内容和时间分布均匀的内容
                sampled_texts = self._smart_sample_content(all_texts, content_metadata, 20)
                all_texts = sampled_texts
            
            # 计算统计信息
            stats = {
                "total_posts": len(reddit_user.posts),
                "total_comments": len(reddit_user.comments),
                "processed_texts": len(all_texts),
                "avg_content_length": sum(len(text) for text in all_texts) / len(all_texts),
                "subreddit_count": len(set(meta["subreddit"] for meta in content_metadata)),
                "date_range": {
                    "start": min(meta["timestamp"] for meta in content_metadata),
                    "end": max(meta["timestamp"] for meta in content_metadata)
                }
            }
            
            return {
                "texts": all_texts,
                "context": context,
                "metadata": content_metadata,
                "stats": stats
            }
            
        except Exception as e:
            logger.error(f"准备内容数据时发生错误: {e}")
            return None
    
    def _smart_sample_content(self, texts: List[str], metadata: List[Dict], target_count: int) -> List[str]:
        """
        智能采样内容
        
        Args:
            texts: 文本列表
            metadata: 元数据列表
            target_count: 目标数量
            
        Returns:
            List[str]: 采样后的文本
        """
        if len(texts) <= target_count:
            return texts
        
        # 按多个维度评分
        scored_items = []
        for i, (text, meta) in enumerate(zip(texts, metadata)):
            score = 0
            
            # 评分因子
            score += meta["score"] * 0.3  # Reddit评分
            score += min(meta["length"] / 200, 2) * 0.2  # 内容长度（适中加分）
            score += (1 if meta["type"] == "post" else 0.7) * 0.2  # 帖子比评论重要
            
            # 时间分布加分（保持时间线的完整性）
            time_position = i / len(texts)
            time_diversity_bonus = 1 - abs(time_position - 0.5)  # 中间时间段加分
            score += time_diversity_bonus * 0.3
            
            scored_items.append((score, i, text))
        
        # 按评分排序并取前N个
        scored_items.sort(key=lambda x: x[0], reverse=True)
        selected_texts = [item[2] for item in scored_items[:target_count]]
        
        return selected_texts

    async def _build_user_graph_with_ai(self, texts: List[str], context: Dict[str, Any],
                                       semantic_analysis: Dict[str, Any]) -> UserGraph:
        """
        使用AI构建用户图谱

        Args:
            texts: 用户文本内容
            context: 用户上下文信息
            semantic_analysis: 语义分析结果

        Returns:
            UserGraph: 构建的用户图谱
        """
        try:
            # 准备内容摘要
            content_summary = "\n".join(texts[:10])  # 取前10条内容

            # 构建AI提示
            prompt = f"""
请基于以下Reddit用户的发言内容，构建一个人格图谱。

用户信息：
- 用户名：{context.get('username', 'unknown')}
- 发言数量：{len(texts)}

用户发言内容：
{content_summary}

语义分析结果：
- 主要话题：{semantic_analysis.get('parsed_query', {}).get('topics', [])}
- 情绪状态：{semantic_analysis.get('parsed_query', {}).get('emotional_state', {})}

请以JSON格式返回人格图谱，包含节点和边：
{{
    "nodes": [
        {{
            "node_id": "节点ID",
            "node_type": "PERSONALITY|INTEREST|BELIEF|EMOTION|BEHAVIOR",
            "content": "节点描述",
            "weight": 0.8,
            "metadata": {{}}
        }}
    ],
    "edges": [
        {{
            "source_id": "源节点ID",
            "target_id": "目标节点ID",
            "relation_type": "INFLUENCES|SUPPORTS|CONFLICTS|CAUSES",
            "weight": 0.7,
            "evidence": "关系证据"
        }}
    ]
}}

请确保：
1. 至少包含5-10个节点
2. 节点类型多样化
3. 边关系合理且有证据支持
4. 权重反映重要性
"""

            response = await self.ai_service.get_completion(
                prompt=prompt,
                max_tokens=2000,
                temperature=0.4,
                system_prompt="你是一个专业的心理分析师，擅长构建人格图谱。请严格按照JSON格式返回结果。"
            )

            # 解析AI响应
            import json
            graph_data = json.loads(self.ai_service._extract_json_from_response(response))

            # 构建UserGraph对象
            user_graph = UserGraph(user_id=context.get('username', 'unknown'))

            # 添加节点
            for node_data in graph_data.get("nodes", []):
                node = GraphNode(
                    node_id=node_data["node_id"],
                    node_type=self._parse_node_type(node_data["node_type"]),
                    content=node_data["content"],
                    weight=node_data.get("weight", 0.5),
                    metadata=node_data.get("metadata", {})
                )
                user_graph.add_node(node)

            # 添加边
            for edge_data in graph_data.get("edges", []):
                edge = GraphEdge(
                    source_id=edge_data["source_id"],
                    target_id=edge_data["target_id"],
                    relation_type=self._parse_relation_type(edge_data["relation_type"]),
                    weight=edge_data.get("weight", 0.5),
                    evidence=edge_data.get("evidence", "")
                )
                user_graph.add_edge(edge)

            return user_graph

        except Exception as e:
            logger.error(f"AI构建用户图谱失败: {e}")
            # 返回基础图谱
            return self._create_basic_graph(context.get('username', 'unknown'))

    def _parse_node_type(self, type_str: str) -> NodeType:
        """解析节点类型"""
        type_mapping = {
            "PERSONALITY": NodeType.PERSONALITY,
            "INTEREST": NodeType.INTEREST,
            "BELIEF": NodeType.BELIEF,
            "EMOTION": NodeType.EMOTION,
            "BEHAVIOR": NodeType.BEHAVIOR,
            "TOPIC": NodeType.TOPIC,
            "VALUE": NodeType.VALUE
        }
        return type_mapping.get(type_str.upper(), NodeType.PERSONALITY)

    def _parse_relation_type(self, type_str: str) -> RelationType:
        """解析关系类型"""
        type_mapping = {
            "INFLUENCES": RelationType.INFLUENCES,
            "SUPPORTS": RelationType.SUPPORTS,
            "CONFLICTS": RelationType.CONFLICTS,
            "CAUSES": RelationType.CAUSES,
            "RELATES_TO": RelationType.RELATES_TO
        }
        return type_mapping.get(type_str.upper(), RelationType.RELATES_TO)

    def _create_basic_graph(self, user_id: str) -> UserGraph:
        """创建基础图谱（当AI构建失败时使用）"""
        graph = UserGraph(user_id=user_id)

        # 添加基础节点
        basic_node = GraphNode(
            node_id=f"basic_{user_id}",
            node_type=NodeType.PERSONALITY,
            content="基础人格特征",
            weight=0.5,
            metadata={"source": "fallback"}
        )
        graph.add_node(basic_node)

        return graph
    
    async def _enhance_graph_with_semantic_analysis(self, user_graph: UserGraph, 
                                                  semantic_analysis: Dict[str, Any]) -> UserGraph:
        """
        基于语义分析结果增强图谱
        
        Args:
            user_graph: 基础用户图谱
            semantic_analysis: 语义分析结果
            
        Returns:
            UserGraph: 增强后的图谱
        """
        try:
            enhanced_graph = user_graph
            
            # 1. 添加从语义分析中提取的节点
            if semantic_analysis.get("success") and semantic_analysis.get("parsed_query"):
                parsed_query = semantic_analysis["parsed_query"]
                
                # 添加情绪节点
                for emotion, intensity in parsed_query.emotional_state.items():
                    if intensity > 0.4:  # 显著情绪
                        emotion_node = GraphNode(
                            node_id=f"emotion_{emotion}_{user_graph.user_id}",
                            node_type=NodeType.EMOTION,
                            content=f"{emotion}情绪（强度：{intensity:.2f}）",
                            weight=intensity,
                            metadata={
                                "source": "semantic_analysis",
                                "intensity": intensity,
                                "emotion_type": emotion
                            }
                        )
                        enhanced_graph.add_node(emotion_node)
                
                # 添加话题节点
                for topic in parsed_query.topics:
                    topic_node = GraphNode(
                        node_id=f"topic_{topic}_{user_graph.user_id}",
                        node_type=NodeType.TOPIC,
                        content=f"关注话题：{topic}",
                        weight=0.7,
                        metadata={
                            "source": "semantic_analysis",
                            "topic": topic
                        }
                    )
                    enhanced_graph.add_node(topic_node)
                
                # 添加价值观节点
                for key, value in parsed_query.values_info.items():
                    if value and str(value).lower() not in ["unknown", "未知"]:
                        belief_node = GraphNode(
                            node_id=f"belief_{key}_{user_graph.user_id}",
                            node_type=NodeType.BELIEF,
                            content=f"{key}：{value}",
                            weight=0.8,
                            metadata={
                                "source": "semantic_analysis",
                                "value_dimension": key,
                                "value": value
                            }
                        )
                        enhanced_graph.add_node(belief_node)
            
            # 2. 添加从用户特征分析中提取的节点
            if semantic_analysis.get("user_characteristics"):
                char_data = semantic_analysis["user_characteristics"]
                
                # 添加性格特征节点
                personality = char_data.get("personality_traits", {})
                for trait, score in personality.items():
                    if score > 0.6:  # 显著特征
                        trait_node = GraphNode(
                            node_id=f"personality_{trait}_{user_graph.user_id}",
                            node_type=NodeType.BELIEF,
                            content=f"性格特征：{trait}（{score:.2f}）",
                            weight=score,
                            metadata={
                                "source": "personality_analysis",
                                "trait": trait,
                                "score": score
                            }
                        )
                        enhanced_graph.add_node(trait_node)
                
                # 添加兴趣节点
                interests = char_data.get("interests_and_values", {}).get("main_interests", [])
                for interest in interests:
                    if interest and interest != "未知":
                        interest_node = GraphNode(
                            node_id=f"interest_{interest}_{user_graph.user_id}",
                            node_type=NodeType.TOPIC,
                            content=f"兴趣：{interest}",
                            weight=0.6,
                            metadata={
                                "source": "interest_analysis",
                                "interest": interest
                            }
                        )
                        enhanced_graph.add_node(interest_node)
            
            # 3. 增强节点间的连接
            enhanced_graph = self._enhance_node_connections(enhanced_graph)
            
            logger.info(f"图谱增强完成，节点数：{len(enhanced_graph.nodes)}")
            return enhanced_graph
            
        except Exception as e:
            logger.error(f"图谱增强时发生错误: {e}")
            return user_graph
    
    def _enhance_node_connections(self, graph: UserGraph) -> UserGraph:
        """
        增强节点间的连接
        
        Args:
            graph: 用户图谱
            
        Returns:
            UserGraph: 增强连接后的图谱
        """
        try:
            # 获取不同类型的节点
            emotion_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.EMOTION]
            belief_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.BELIEF]
            topic_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.TOPIC]
            experience_nodes = [node for node in graph.nodes.values() if node.node_type == NodeType.EXPERIENCE]
            
            # 添加情绪与信念的连接
            for emotion_node in emotion_nodes:
                for belief_node in belief_nodes:
                    if self._should_connect_emotion_belief(emotion_node, belief_node):
                        edge = GraphEdge(
                            source_id=emotion_node.node_id,
                            target_id=belief_node.node_id,
                            relation_type=RelationType.INFLUENCES,
                            weight=0.6,
                            evidence=f"情绪状态影响价值观念"
                        )
                        graph.add_edge(edge)
            
            # 添加话题与情绪的连接
            for topic_node in topic_nodes:
                for emotion_node in emotion_nodes:
                    if self._should_connect_topic_emotion(topic_node, emotion_node):
                        edge = GraphEdge(
                            source_id=topic_node.node_id,
                            target_id=emotion_node.node_id,
                            relation_type=RelationType.TRIGGERS,
                            weight=0.5,
                            evidence=f"话题引发情绪反应"
                        )
                        graph.add_edge(edge)
            
            # 添加经历与信念的连接
            for exp_node in experience_nodes:
                for belief_node in belief_nodes:
                    if self._should_connect_experience_belief(exp_node, belief_node):
                        edge = GraphEdge(
                            source_id=exp_node.node_id,
                            target_id=belief_node.node_id,
                            relation_type=RelationType.LEADS_TO,
                            weight=0.7,
                            evidence=f"经历塑造价值观"
                        )
                        graph.add_edge(edge)
            
            logger.info(f"节点连接增强完成，边数：{len(graph.edges)}")
            return graph
            
        except Exception as e:
            logger.error(f"增强节点连接时发生错误: {e}")
            return graph
    
    def _should_connect_emotion_belief(self, emotion_node: GraphNode, belief_node: GraphNode) -> bool:
        """判断情绪和信念节点是否应该连接"""
        # 简单的连接逻辑，可以根据需要改进
        emotion_type = emotion_node.metadata.get("emotion_type", "")
        belief_content = belief_node.content.lower()
        
        # 基于情绪类型和信念内容的关联性
        if emotion_type == "anxiety" and any(word in belief_content for word in ["risk", "safety", "security"]):
            return True
        if emotion_type == "hope" and any(word in belief_content for word in ["goal", "future", "optimism"]):
            return True
        if emotion_type == "confusion" and any(word in belief_content for word in ["decision", "choice", "direction"]):
            return True
        
        return False
    
    def _should_connect_topic_emotion(self, topic_node: GraphNode, emotion_node: GraphNode) -> bool:
        """判断话题和情绪节点是否应该连接"""
        topic_content = topic_node.content.lower()
        emotion_type = emotion_node.metadata.get("emotion_type", "")
        
        # 基于话题内容和情绪类型的关联性
        if "career" in topic_content and emotion_type in ["anxiety", "confusion"]:
            return True
        if "relationship" in topic_content and emotion_type in ["sadness", "frustration"]:
            return True
        if "personal" in topic_content and emotion_type in ["hope", "determination"]:
            return True
        
        return False
    
    def _should_connect_experience_belief(self, exp_node: GraphNode, belief_node: GraphNode) -> bool:
        """判断经历和信念节点是否应该连接"""
        exp_content = exp_node.content.lower()
        belief_content = belief_node.content.lower()
        
        # 基于内容相似性判断
        common_keywords = ["work", "career", "relationship", "decision", "choice", "goal"]
        exp_keywords = [word for word in common_keywords if word in exp_content]
        belief_keywords = [word for word in common_keywords if word in belief_content]
        
        # 如果有共同关键词，则连接
        return len(set(exp_keywords) & set(belief_keywords)) > 0
    
    async def _build_user_profile(self, reddit_user: RedditUser,
                                user_graph: UserGraph,
                                semantic_analysis: Dict[str, Any]) -> UserProfile:
        """
        构建完整用户画像
        
        Args:
            reddit_user: Reddit用户数据
            user_graph: 用户图谱
            semantic_analysis: 语义分析结果
            
        Returns:
            UserProfile: 完整用户画像
        """
        try:
            # 创建基础用户画像
            user_profile = UserProfile(
                user_id=reddit_user.username,
                original_query=f"Reddit用户{reddit_user.username}的内容分析",
                graph=user_graph,
                version=1
            )
            
            # 计算完整性
            completeness = user_profile.update_completeness()
            
            # 添加额外的元数据
            user_profile.metadata = {
                "reddit_username": reddit_user.username,
                "total_posts": len(reddit_user.posts),
                "total_comments": len(reddit_user.comments),
                "analysis_timestamp": datetime.now(),
                "semantic_confidence": semantic_analysis.get("stats", {}).get("confidence_score", 0.5),
                "completeness_score": completeness
            }
            
            return user_profile
            
        except Exception as e:
            logger.error(f"构建用户画像时发生错误: {e}")
            # 创建基础画像
            return UserProfile(
                user_id=reddit_user.username,
                original_query=f"Reddit用户{reddit_user.username}的内容分析",
                graph=user_graph
            )
    
    async def _generate_graph_explanation(self, user_profile: UserProfile, 
                                        semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """
        生成图谱解释
        
        Args:
            user_profile: 用户画像
            semantic_analysis: 语义分析结果
            
        Returns:
            Dict: 图谱解释
        """
        try:
            # 分析图谱结构
            graph_stats = user_profile.graph.get_node_count_by_type()

            # 兼容 ParsedQuery 对象或字典
            parsed_query_obj = semantic_analysis.get("parsed_query", {})
            if hasattr(parsed_query_obj, "dict"):
                parsed_query = parsed_query_obj.dict()
            elif isinstance(parsed_query_obj, dict):
                parsed_query = parsed_query_obj
            else:
                parsed_query = {}

            # 生成自然语言解释
            explanation_prompt = f"""
请为以下用户人格图谱生成一个清晰的解释说明：

用户：{user_profile.user_id}
图谱节点统计：{graph_stats}
图谱边数：{len(user_profile.graph.edges)}

主要发现：
· 情绪状态：{parsed_query.get('emotional_state', {})}
· 关注话题：{parsed_query.get('topics', [])}
· 用户特征：{semantic_analysis.get('user_characteristics', {}).get('personality_traits', {})}

请生成一个包含以下内容的解释：
1. 用户的整体心理画像
2. 主要的性格特征
3. 图谱中的关键洞察
4. 节点间的重要关系
5. 建议的互动方式

请以JSON格式返回：
{{
    "overall_portrait": "整体心理画像",
    "key_traits": ["特征1", "特征2", "特征3"],
    "main_insights": ["洞察1", "洞察2", "洞察3"],
    "relationship_patterns": ["关系模式1", "关系模式2"],
    "interaction_recommendations": ["建议1", "建议2", "建议3"],
    "confidence_level": "high|medium|low"
}}"""
            
            response = await self.ai_service.get_completion(
                prompt=explanation_prompt,
                max_tokens=2000,
                temperature=0.6
            )
            
            import json
            explanation = json.loads(self.ai_service._extract_json_from_response(response))
            
            return explanation
            
        except Exception as e:
            logger.error(f"生成图谱解释时发生错误: {e}")
            return {
                "overall_portrait": "由于数据限制，无法生成完整的心理画像",
                "key_traits": ["数据不足"],
                "main_insights": ["需要更多数据"],
                "relationship_patterns": ["关系模式不明确"],
                "interaction_recommendations": ["保持开放友好的态度"],
                "confidence_level": "low"
            }
    
    def _calculate_graph_quality(self, user_graph: UserGraph) -> Dict[str, Any]:
        """
        计算图谱质量指标
        
        Args:
            user_graph: 用户图谱
            
        Returns:
            Dict: 质量指标
        """
        try:
            node_count = len(user_graph.nodes)
            edge_count = len(user_graph.edges)
            node_types = user_graph.get_node_count_by_type()
            
            # 计算各种质量指标
            quality_metrics = {
                "node_count": node_count,
                "edge_count": edge_count,
                "node_types_distribution": node_types,
                "graph_density": edge_count / max(node_count * (node_count - 1), 1),
                "avg_node_degree": (2 * edge_count) / max(node_count, 1),
                "completeness_score": user_graph.calculate_completeness() if hasattr(user_graph, 'calculate_completeness') else 0.5,
                "structural_complexity": self._calculate_structural_complexity(user_graph),
                "information_richness": self._calculate_information_richness(user_graph),
                "overall_quality": "high" if node_count >= 10 and edge_count >= 5 else "medium" if node_count >= 5 else "low"
            }
            
            return quality_metrics
            
        except Exception as e:
            logger.error(f"计算图谱质量时发生错误: {e}")
            return {
                "node_count": 0,
                "edge_count": 0,
                "overall_quality": "low"
            }
    
    def _calculate_structural_complexity(self, user_graph: UserGraph) -> float:
        """计算结构复杂度"""
        try:
            node_count = len(user_graph.nodes)
            edge_count = len(user_graph.edges)
            
            if node_count == 0:
                return 0.0
            
            # 基于节点数和边数的复杂度
            complexity = (edge_count / node_count) / 3  # 标准化到0-1范围
            return min(complexity, 1.0)
            
        except Exception:
            return 0.0
    
    def _calculate_information_richness(self, user_graph: UserGraph) -> float:
        """计算信息丰富度"""
        try:
            # 基于节点类型多样性和内容长度
            node_types = set(node.node_type for node in user_graph.nodes.values())
            type_diversity = len(node_types) / 4  # 4种类型
            
            # 基于节点内容的平均长度
            avg_content_length = sum(len(node.content) for node in user_graph.nodes.values()) / len(user_graph.nodes)
            content_richness = min(avg_content_length / 100, 1.0)  # 标准化
            
            return (type_diversity + content_richness) / 2
            
        except Exception:
            return 0.0
    
    async def close(self):
        """关闭服务"""
        try:
            await self.ai_service.close()
            logger.info("人格图谱构建器已关闭")
        except Exception as e:
            logger.error(f"关闭人格图谱构建器时发生错误: {e}")

# 便捷函数
async def build_personality_graph(reddit_user: RedditUser, 
                                semantic_analysis: Dict[str, Any]) -> Dict[str, Any]:
    """
    便捷函数：构建人格图谱
    
    Args:
        reddit_user: Reddit用户数据
        semantic_analysis: 语义分析结果
        
    Returns:
        Dict: 图谱构建结果
    """
    builder = PersonalityGraphBuilder()
    try:
        result = await builder.build_personality_graph(reddit_user, semantic_analysis)
        return result
    finally:
        await builder.close()

if __name__ == "__main__":
    # 测试代码
    async def test_graph_builder():
        # 这里应该有实际的测试代码
        print("人格图谱构建器测试")
        
    # 运行测试
    # asyncio.run(test_graph_builder()) 