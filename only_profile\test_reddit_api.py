"""
测试Reddit API连接
"""
import asyncio
import asyncpraw
import os
from local_config import config

async def test_reddit_connection():
    """测试Reddit API连接"""
    print("测试Reddit API连接...")
    
    try:
        # 创建Reddit实例
        reddit = asyncpraw.Reddit(
            client_id=os.getenv("REDDIT_CLIENT_ID", "6BNG2nc5jmjl3XQ03HqmYA"),
            client_secret=os.getenv("REDDIT_CLIENT_SECRET", "97tz1THL8PcNlryAy6T8NcES6GEpwg"),
            user_agent=os.getenv("REDDIT_USER_AGENT", "OnlyProfile/1.0 by RM-Li"),
            requestor_kwargs={"session": None}
        )
        
        print("✅ Reddit实例创建成功")
        
        # 测试获取评论
        comment_id = "n1lgpq6"
        print(f"尝试获取评论: {comment_id}")
        
        comment = await reddit.comment(id=comment_id)
        print(f"评论对象: {comment}")
        print(f"评论类型: {type(comment)}")
        
        if comment is None:
            print("❌ 评论对象为None")
            return
            
        print("尝试加载评论数据...")
        await comment.load()
        print("✅ 评论数据加载成功")
        
        print(f"评论内容: {comment.body[:100]}...")
        print(f"评论作者: {comment.author}")
        
        if comment.author:
            print(f"作者名称: {comment.author.name}")
        else:
            print("❌ 评论作者为None")
            
        # 关闭连接
        if hasattr(reddit, '_core') and hasattr(reddit._core, '_requestor'):
            await reddit._core._requestor._http.close()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_reddit_connection())
